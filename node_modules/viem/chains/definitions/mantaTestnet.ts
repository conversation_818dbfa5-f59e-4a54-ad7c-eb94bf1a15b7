import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const mantaTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 3_441_005,
  name: 'Manta Pacific Testnet',
  network: 'manta-testnet',
  nativeCurrency: {
    decimals: 18,
    name: 'ETH',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: { http: ['https://manta-testnet.calderachain.xyz/http'] },
    public: { http: ['https://manta-testnet.calderachain.xyz/http'] },
  },
  blockExplorers: {
    etherscan: {
      name: 'Manta Testnet Explorer',
      url: 'https://pacific-explorer.testnet.manta.network',
    },
    default: {
      name: 'Manta Testnet Explorer',
      url: 'https://pacific-explorer.testnet.manta.network',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 419915,
    },
  },
  testnet: true,
})
