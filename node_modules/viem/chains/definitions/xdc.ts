import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const xdc = /*#__PURE__*/ define<PERSON>hain({
  id: 50,
  name: 'XinFin Network',
  network: 'xdc',
  nativeCurrency: {
    decimals: 18,
    name: 'X<PERSON>',
    symbol: 'XDC',
  },
  rpcUrls: {
    default: { http: ['https://rpc.xinfin.network'] },
    public: { http: ['https://rpc.xinfin.network'] },
  },
  blockExplorers: {
    xinfin: { name: 'XinFin', url: 'https://explorer.xinfin.network' },
    default: { name: 'Blocksscan', url: 'https://xdc.blocksscan.io' },
  },
})
