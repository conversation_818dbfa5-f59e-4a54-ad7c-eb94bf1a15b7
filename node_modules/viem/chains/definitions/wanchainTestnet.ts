import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const wanchainTestnet = /*#__PURE__*/ defineChain({
  id: 999,
  name: 'Wanchain Testnet',
  network: 'wanchainTestnet',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON><PERSON>', symbol: 'WANt', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://gwan-ssl.wandevs.org:46891'],
    },
    public: {
      http: ['https://gwan-ssl.wandevs.org:46891'],
    },
  },
  blockExplorers: {
    etherscan: {
      name: 'WanScanTest',
      url: 'https://wanscan.org',
    },
    default: {
      name: 'WanScanTest',
      url: 'https://wanscan.org',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 24743448,
    },
  },
  testnet: true,
})
