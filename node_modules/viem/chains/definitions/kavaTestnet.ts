import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const kavaTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 2221,
  name: 'Kava EVM Testnet',
  network: 'kava-testnet',
  nativeCurrency: {
    name: 'Ka<PERSON>',
    symbol: 'KAVA',
    decimals: 18,
  },
  rpcUrls: {
    public: { http: ['https://evm.testnet.kava.io'] },
    default: { http: ['https://evm.testnet.kava.io'] },
  },
  blockExplorers: {
    default: {
      name: 'Kava EVM Testnet Explorer',
      url: 'https://testnet.kavascan.com/',
    },
  },
  contracts: {
    multicall3: {
      address: '0xDf1D724A7166261eEB015418fe8c7679BBEa7fd6',
      blockCreated: 7242179,
    },
  },
  testnet: true,
})
