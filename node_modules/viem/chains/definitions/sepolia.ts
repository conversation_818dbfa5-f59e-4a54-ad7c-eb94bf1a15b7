import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const sepolia = /*#__PURE__*/ define<PERSON>hain({
  id: 11_155_111,
  network: 'sepolia',
  name: '<PERSON><PERSON>',
  nativeCurrency: { name: '<PERSON><PERSON>ther', symbol: 'SEP', decimals: 18 },
  rpcUrls: {
    alchemy: {
      http: ['https://eth-sepolia.g.alchemy.com/v2'],
      webSocket: ['wss://eth-sepolia.g.alchemy.com/v2'],
    },
    infura: {
      http: ['https://sepolia.infura.io/v3'],
      webSocket: ['wss://sepolia.infura.io/ws/v3'],
    },
    default: {
      http: ['https://rpc.sepolia.org'],
    },
    public: {
      http: ['https://rpc.sepolia.org'],
    },
  },
  blockExplorers: {
    etherscan: {
      name: 'Etherscan',
      url: 'https://sepolia.etherscan.io',
    },
    default: {
      name: 'Etherscan',
      url: 'https://sepolia.etherscan.io',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 751532,
    },
    ensRegistry: { address: '******************************************' },
    ensUniversalResolver: {
      address: '******************************************',
      blockCreated: 3914906,
    },
  },
  testnet: true,
})
