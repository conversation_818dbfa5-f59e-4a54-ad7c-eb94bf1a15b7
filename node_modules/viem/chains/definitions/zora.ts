import { define<PERSON>hain } from '../../utils/chain/defineChain.js'
import { formattersOptimism } from '../optimism/formatters.js'

export const zora = /*#__PURE__*/ define<PERSON>hain(
  {
    id: 7777777,
    name: '<PERSON><PERSON>',
    network: 'zora',
    nativeCurrency: {
      decimals: 18,
      name: 'Ether',
      symbol: 'ETH',
    },
    rpcUrls: {
      default: {
        http: ['https://rpc.zora.energy'],
        webSocket: ['wss://rpc.zora.energy'],
      },
      public: {
        http: ['https://rpc.zora.energy'],
        webSocket: ['wss://rpc.zora.energy'],
      },
    },
    blockExplorers: {
      default: { name: 'Explorer', url: 'https://explorer.zora.energy' },
    },
    contracts: {
      multicall3: {
        address: '******************************************',
        blockCreated: 5882,
      },
    },
  },
  {
    formatters: formattersOptimism,
  },
)
