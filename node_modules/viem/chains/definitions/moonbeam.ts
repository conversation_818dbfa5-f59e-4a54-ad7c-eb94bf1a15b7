import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const moonbeam = /*#__PURE__*/ defineChain({
  id: 1284,
  name: 'Moonbeam',
  network: 'moonbeam',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON><PERSON>',
    symbol: '<PERSON><PERSON><PERSON>',
  },
  rpcUrls: {
    public: {
      http: ['https://moonbeam.public.blastapi.io'],
      webSocket: ['wss://moonbeam.public.blastapi.io'],
    },
    default: {
      http: ['https://moonbeam.public.blastapi.io'],
      webSocket: ['wss://moonbeam.public.blastapi.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Moonscan',
      url: 'https://moonscan.io',
    },
    etherscan: {
      name: 'Moonscan',
      url: 'https://moonscan.io',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 609002,
    },
  },
  testnet: false,
})
