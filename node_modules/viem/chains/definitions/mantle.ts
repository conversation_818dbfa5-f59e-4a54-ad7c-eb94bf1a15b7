import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const mantle = /*#__PURE__*/ defineChain({
  id: 5000,
  name: 'Mantle',
  network: 'mantle',
  nativeCurrency: {
    decimals: 18,
    name: 'M<PERSON>',
    symbol: 'MNT',
  },
  rpcUrls: {
    default: { http: ['https://rpc.mantle.xyz'] },
    public: { http: ['https://rpc.mantle.xyz'] },
  },
  blockExplorers: {
    etherscan: {
      name: 'Mantle Explorer',
      url: 'https://explorer.mantle.xyz',
    },
    default: {
      name: 'Mantle Explorer',
      url: 'https://explorer.mantle.xyz',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 304717,
    },
  },
})
