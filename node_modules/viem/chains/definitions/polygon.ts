import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const polygon = /*#__PURE__*/ define<PERSON>hain({
  id: 137,
  name: 'Polygon',
  network: 'matic',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'MA<PERSON><PERSON>', decimals: 18 },
  rpcUrls: {
    alchemy: {
      http: ['https://polygon-mainnet.g.alchemy.com/v2'],
      webSocket: ['wss://polygon-mainnet.g.alchemy.com/v2'],
    },
    infura: {
      http: ['https://polygon-mainnet.infura.io/v3'],
      webSocket: ['wss://polygon-mainnet.infura.io/ws/v3'],
    },
    default: {
      http: ['https://polygon-rpc.com'],
    },
    public: {
      http: ['https://polygon-rpc.com'],
    },
  },
  blockExplorers: {
    etherscan: {
      name: 'PolygonScan',
      url: 'https://polygonscan.com',
    },
    default: {
      name: 'PolygonScan',
      url: 'https://polygonscan.com',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 25770160,
    },
  },
})
