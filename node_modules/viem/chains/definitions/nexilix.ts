import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const nexilix = /*#__PURE__*/ define<PERSON>hain({
  id: 240,
  name: 'Nexilix Smart Chain',
  network: 'nexilix',
  nativeCurrency: {
    decimals: 18,
    name: 'Nexilix',
    symbol: 'NEXILIX',
  },
  rpcUrls: {
    default: { http: ['https://rpcurl.pos.nexilix.com'] },
    public: { http: ['https://rpcurl.pos.nexilix.com'] },
  },
  blockExplorers: {
    etherscan: { name: 'NexilixScan', url: 'https://scan.nexilix.com' },
    default: { name: 'NexilixScan', url: 'https://scan.nexilix.com' },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 74448,
    },
  },
})
