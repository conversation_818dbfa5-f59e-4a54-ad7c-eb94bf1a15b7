import { define<PERSON>hain } from '../../utils/chain/defineChain.js'
import { formattersOptimism } from '../optimism/formatters.js'

export const pgn = /*#__PURE__*/ define<PERSON>hain(
  {
    id: 424,
    network: 'pgn',
    name: '<PERSON><PERSON><PERSON>',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
      default: {
        http: ['https://rpc.publicgoods.network'],
      },
      public: {
        http: ['https://rpc.publicgoods.network'],
      },
    },
    blockExplorers: {
      default: {
        name: 'PGN Explorer',
        url: 'https://explorer.publicgoods.network',
      },
      blocksout: {
        name: 'PGN Explorer',
        url: 'https://explorer.publicgoods.network',
      },
    },
    contracts: {
      multicall3: {
        address: '******************************************',
        blockCreated: 3380209,
      },
    },
  },
  {
    formatters: formattersOptimism,
  },
)
