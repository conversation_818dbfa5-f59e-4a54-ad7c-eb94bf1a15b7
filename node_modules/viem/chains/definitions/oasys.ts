import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const oasys = /*#__PURE__*/ define<PERSON>hain({
  id: 248,
  name: 'Oasys',
  network: 'oasys',
  nativeCurrency: { name: 'Oas<PERSON>', symbol: 'OAS', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.mainnet.oasys.games'],
    },
    public: {
      http: ['https://rpc.mainnet.oasys.games'],
    },
  },
  blockExplorers: {
    default: {
      name: 'OasysScan',
      url: 'https://scan.oasys.games',
    },
  },
})
