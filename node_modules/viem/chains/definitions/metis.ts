import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const metis = /*#__PURE__*/ define<PERSON>hain({
  id: 1_088,
  name: 'Met<PERSON>',
  network: 'andromeda',
  nativeCurrency: {
    decimals: 18,
    name: 'Met<PERSON>',
    symbol: 'METIS',
  },
  rpcUrls: {
    default: { http: ['https://andromeda.metis.io/?owner=1088'] },
    public: { http: ['https://andromeda.metis.io/?owner=1088'] },
  },
  blockExplorers: {
    default: {
      name: 'Andromeda Explorer',
      url: 'https://andromeda-explorer.metis.io',
    },
  },
  contracts: {
    multicall3: {
      address: '0xca11bde05977b3631167028862be2a173976ca11',
      blockCreated: 2338552,
    },
  },
})
