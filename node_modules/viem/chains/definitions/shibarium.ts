import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const shibarium = /*#__PURE__*/ defineChain({
  id: 109,
  name: 'Shibarium',
  network: 'shibarium',
  nativeCurrency: { name: '<PERSON>', symbol: 'BONE', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.shibrpc.com'],
    },
    public: {
      http: ['https://rpc.shibrpc.com'],
    },
  },
  blockExplorers: {
    etherscan: {
      name: 'Blockscout',
      url: 'https://shibariumscan.io',
    },
    default: {
      name: 'Blockscout',
      url: 'https://shibariumscan.io',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 265900,
    },
  },
})
