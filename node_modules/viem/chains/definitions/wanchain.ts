import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const wanchain = /*#__PURE__*/ defineChain({
  id: 888,
  name: 'Wanchain',
  network: 'wanchain',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON><PERSON>', symbol: 'WAN', decimals: 18 },
  rpcUrls: {
    default: {
      http: [
        'https://gwan-ssl.wandevs.org:56891',
        'https://gwan2-ssl.wandevs.org',
      ],
    },
    public: {
      http: [
        'https://gwan-ssl.wandevs.org:56891',
        'https://gwan2-ssl.wandevs.org',
      ],
    },
  },
  blockExplorers: {
    etherscan: {
      name: 'Wan<PERSON><PERSON>',
      url: 'https://wanscan.org',
    },
    default: {
      name: '<PERSON>S<PERSON>',
      url: 'https://wanscan.org',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 25312390,
    },
  },
})
