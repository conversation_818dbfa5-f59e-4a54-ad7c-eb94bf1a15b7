import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const lukso = /*#__PURE__*/ define<PERSON>hain({
  id: 42,
  network: 'lukso',
  name: '<PERSON><PERSON><PERSON><PERSON>',
  nativeCurrency: {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    symbol: 'LYX',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.mainnet.lukso.network'],
      webSocket: ['wss://ws-rpc.mainnet.lukso.network'],
    },
    public: {
      http: ['https://rpc.mainnet.lukso.network'],
      webSocket: ['wss://ws-rpc.mainnet.lukso.network'],
    },
  },
  blockExplorers: {
    default: {
      name: 'LUKSO Mainnet Explorer',
      url: 'https://explorer.execution.mainnet.lukso.network',
    },
  },
})
