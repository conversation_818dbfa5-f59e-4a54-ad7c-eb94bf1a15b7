import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'
import { formattersOptimism } from '../optimism/formatters.js'

export const optimismGoerli = /*#__PURE__*/ define<PERSON>hain(
  {
    id: 420,
    name: 'Optimism Goer<PERSON>',
    network: 'optimism-goerli',
    nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
      alchemy: {
        http: ['https://opt-goerli.g.alchemy.com/v2'],
        webSocket: ['wss://opt-goerli.g.alchemy.com/v2'],
      },
      infura: {
        http: ['https://optimism-goerli.infura.io/v3'],
        webSocket: ['wss://optimism-goerli.infura.io/ws/v3'],
      },
      default: {
        http: ['https://goerli.optimism.io'],
      },
      public: {
        http: ['https://goerli.optimism.io'],
      },
    },
    blockExplorers: {
      etherscan: {
        name: 'Etherscan',
        url: 'https://goerli-optimism.etherscan.io',
      },
      default: {
        name: 'Etherscan',
        url: 'https://goerli-optimism.etherscan.io',
      },
    },
    contracts: {
      multicall3: {
        address: '******************************************',
        blockCreated: 49461,
      },
    },
    testnet: true,
  },
  {
    formatters: formattersOptimism,
  },
)
