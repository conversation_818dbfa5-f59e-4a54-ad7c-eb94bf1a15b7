import type { Address } from 'abitype';
import type { By<PERSON><PERSON><PERSON>y, Hex, SignableMessage } from '../../types/misc.js';
import type { ErrorType } from '../../errors/utils.js';
import { type HashMessageErrorType } from './hashMessage.js';
import { type RecoverAddressErrorType } from './recoverAddress.js';
export type RecoverMessageAddressParameters = {
    message: SignableMessage;
    signature: Hex | ByteArray;
};
export type RecoverMessageAddressReturnType = Address;
export type RecoverMessageAddressErrorType = HashMessageErrorType | RecoverAddressErrorType | ErrorType;
export declare function recoverMessageAddress({ message, signature, }: RecoverMessageAddressParameters): Promise<RecoverMessageAddressReturnType>;
//# sourceMappingURL=recoverMessageAddress.d.ts.map