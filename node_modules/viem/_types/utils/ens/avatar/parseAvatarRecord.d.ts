import type { Client } from '../../../clients/createClient.js';
import type { Transport } from '../../../clients/transports/createTransport.js';
import type { ErrorType } from '../../../errors/utils.js';
import type { Chain } from '../../../types/chain.js';
import type { AssetGatewayUrls } from '../../../types/ens.js';
import { type GetJsonImageErrorType, type GetMetadataAvatarUriErrorType, type GetNftTokenUriErrorType, type ParseAvatarUriErrorType, type ParseNftUriErrorType, type ResolveAvatarUriErrorType } from './utils.js';
export type ParseAvatarRecordErrorType = ParseNftAvatarUriErrorType | ParseAvatarUriErrorType | ErrorType;
export declare function parseAvatarRecord<T<PERSON><PERSON><PERSON> extends Chain | undefined>(client: Client<Transport, TChain>, { gatewayUrls, record, }: {
    gatewayUrls?: AssetGatewayUrls;
    record: string;
}): Promise<string>;
export type ParseNftAvatarUriErrorType = ParseNftUriErrorType | GetNftTokenUriErrorType | ResolveAvatarUriErrorType | ParseAvatarUriErrorType | GetJsonImageErrorType | GetMetadataAvatarUriErrorType | ErrorType;
//# sourceMappingURL=parseAvatarRecord.d.ts.map