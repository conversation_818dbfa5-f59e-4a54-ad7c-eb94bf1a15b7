const { utils, Wallet } = require('ethers');
const qrcode = require('qrcode-terminal');

// Demo function untuk menunjukkan flow tanpa registrasi real
async function demoFlow() {
  console.log('🎬 DEMO: Farcaster Auto Registration Flow\n');
  console.log('💡 Ini hanya demo - tidak ada registrasi real yang terjadi\n');
  
  // Simulasi kalkulasi biaya
  console.log('💰 BREAKDOWN BIAYA (SIMULASI):');
  console.log('├─ App FID Registration: $0.10');
  console.log('├─ User Registration: $0.20');
  console.log('├─ Gas Fee (estimasi): $0.05');
  console.log('├─ Safety Buffer (20%): $0.07');
  console.log('└─ TOTAL DIBUTUHKAN: 0.000168 ETH (~$0.42)\n');
  
  // Generate wallet demo
  const wallet = Wallet.createRandom();
  console.log('🎯 Wallet demo dibuat!');
  console.log('📍 Address:', wallet.address);
  console.log('🔑 Private Key:', wallet.privateKey);
  console.log('📝 Mnemonic:', wallet.mnemonic.phrase, '\n');
  
  // <PERSON><PERSON><PERSON><PERSON> instruksi transfer
  console.log('📱 KIRIM ETH KE ADDRESS INI:');
  console.log('┌─────────────────────────────────────────────┐');
  console.log('│  ' + wallet.address + '  │');
  console.log('└─────────────────────────────────────────────┘\n');
  
  console.log('💸 KIRIM EXACTLY: 0.000168 ETH');
  console.log('🔗 Network: Optimism Mainnet');
  console.log('⚠️  PENTING: Kirim ke Optimism, bukan Ethereum mainnet!\n');
  
  // QR Code
  console.log('📱 QR Code untuk wallet:');
  qrcode.generate(wallet.address, { small: true });
  console.log('');
  
  // Simulasi monitoring balance
  console.log('⏳ Simulasi monitoring balance...\n');
  
  for (let i = 0; i <= 100; i += 20) {
    const progressBar = '█'.repeat(Math.floor(i / 5)) + '░'.repeat(20 - Math.floor(i / 5));
    const currentBalance = (0.000168 * i / 100).toFixed(6);
    
    process.stdout.write(`\r💰 Balance: ${currentBalance} / 0.000168 ETH [${progressBar}] ${i}%`);
    
    // Simulasi delay
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n✅ Balance cukup! Memulai registrasi...\n');
  
  // Simulasi proses registrasi
  const steps = [
    '📋 Mengecek App FID...',
    '✅ App FID berhasil dibuat: 12345',
    '✍️ Membuat Register signature...',
    '✅ Register signature berhasil dibuat',
    '🔑 Membuat account key...',
    '✅ Account key berhasil dibuat',
    '📝 Membuat Signed Key Request...',
    '✅ Signed Key Request berhasil dibuat',
    '✍️ Membuat Add signature...',
    '✅ Add signature berhasil dibuat',
    '🚀 Registrasi user dengan Bundler...',
    '✅ Registrasi berhasil! Transaction hash: 0xabcdef1234567890...'
  ];
  
  for (const step of steps) {
    console.log(step);
    await new Promise(resolve => setTimeout(resolve, 800));
  }
  
  console.log('\n🎉 REGISTRASI BERHASIL! (DEMO)');
  console.log('┌─────────────────────────────────────────────┐');
  console.log('│                 SUMMARY                     │');
  console.log('├─────────────────────────────────────────────┤');
  console.log('│ Address: ' + wallet.address.substring(0, 20) + '...     │');
  console.log('│ Transaction: 0xabcdef1234567890...          │');
  console.log('│ ETH Used: 0.000156 ETH                      │');
  console.log('│ ETH Remaining: 0.000012 ETH                 │');
  console.log('│ Saved vs App: $0.61                        │');
  console.log('└─────────────────────────────────────────────┘');
  console.log('💾 Detail lengkap tersimpan di: ./wallets/' + wallet.address + '.json');
  
  console.log('\n🎬 DEMO SELESAI!');
  console.log('💡 Untuk registrasi real, jalankan: npm start');
  console.log('💰 Jangan lupa bridge ETH ke Optimism dulu!');
}

// Jalankan demo
demoFlow().catch(console.error);
