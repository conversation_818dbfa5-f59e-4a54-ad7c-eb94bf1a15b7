# Farcaster Bot - Registrasi Akun Otomatis

Bot untuk registrasi akun Farcaster secara otomatis menggunakan Bundler contract sesuai dokumentasi resmi.

## 🚨 PENTING - Baca Sebelum Menggunakan!

Script ini telah diperbaiki sesuai dengan **dokumentasi resmi Farcaster** dan menggunakan:
- ✅ **Optimism Mainnet** (bukan Base)
- ✅ **Bundler contract** untuk registrasi lengkap
- ✅ **App FID** dan **User registration** terpisah
- ✅ **Ed25519 keypair** untuk account key
- ✅ **EIP-712 signatures** yang benar

## 📋 Persyaratan

1. **2 Wallet dengan ETH di Optimism Mainnet**:
   - **App Wallet**: Untuk registrasi App FID
   - **User Wallet**: Untuk user yang akan didaftarkan
   - Minimal 0.01 ETH di masing-masing wallet untuk gas fee

2. **Node.js** versi 16 atau lebih baru

## 🛠️ Instalasi

1. **Clone atau download script ini**
2. **Install dependencies**:
   ```bash
   npm install
   ```

## ⚙️ Konfigurasi

1. **Edit file `bot.js`**
2. **Cari bagian KONFIGURASI** (sekitar baris 254):
   ```javascript
   // KONFIGURASI - GANTI DENGAN PRIVATE KEY ANDA!
   const APP_PRIVATE_KEY = '0x00'; // Ganti dengan private key app Anda
   const USER_PRIVATE_KEY = '0x00'; // Ganti dengan private key user yang akan didaftarkan
   ```

3. **Ganti dengan private key Anda**:
   ```javascript
   const APP_PRIVATE_KEY = '0xYOUR_APP_PRIVATE_KEY_HERE';
   const USER_PRIVATE_KEY = '0xYOUR_USER_PRIVATE_KEY_HERE';
   ```

## 🚀 Cara Menjalankan

1. **Pastikan kedua wallet punya ETH di Optimism Mainnet**
2. **Jalankan script**:
   ```bash
   npm start
   ```

## 📊 Proses yang Dilakukan

Script akan melakukan langkah-langkah berikut:

1. **Cek App FID**: Registrasi App FID jika belum ada
2. **Register Signature**: Buat EIP-712 signature untuk registrasi user
3. **Account Key**: Buat Ed25519 keypair untuk user
4. **Signed Key Request**: Buat request yang ditandatangani app
5. **Add Signature**: Buat signature untuk menambah key ke user
6. **Bundler Registration**: Registrasi lengkap melalui Bundler contract

## 📁 Output

Setelah berhasil, script akan membuat:
- **Folder `wallets/`**: Berisi file JSON dengan informasi user
- **File `{user_address}.json`**: Berisi private key, account key, dan transaction hash

## 💰 Estimasi Biaya

- **App FID Registration**: ~0.005-0.01 ETH
- **User Registration**: ~0.005-0.01 ETH
- **Total**: ~0.01-0.02 ETH per user

## 🔗 Bridge ETH ke Optimism

Jika Anda belum punya ETH di Optimism:
- **Official Bridge**: https://app.optimism.io/bridge
- **Alternative**: Gunakan exchange yang support Optimism

## ⚠️ Keamanan

- **Jangan share private key** Anda dengan siapa pun
- **Backup file wallet** yang dihasilkan dengan aman
- **Gunakan wallet terpisah** untuk testing

## 🐛 Troubleshooting

### Error: "Wallet tidak punya ETH"
- Pastikan wallet punya ETH di **Optimism Mainnet** (bukan Ethereum mainnet)
- Cek balance di: https://optimistic.etherscan.io/

### Error: "APP_PRIVATE_KEY harus diisi"
- Edit file `bot.js` dan ganti private key di bagian KONFIGURASI

### Error: "Failed to generate signature"
- Pastikan private key format benar (dimulai dengan 0x)
- Pastikan wallet punya ETH untuk gas fee

## 📚 Referensi

- [Dokumentasi Farcaster](https://docs.farcaster.xyz/developers/guides/accounts/create-account)
- [Bundler Contract](https://docs.farcaster.xyz/reference/contracts/reference/bundler)
- [Optimism Bridge](https://app.optimism.io/bridge)
