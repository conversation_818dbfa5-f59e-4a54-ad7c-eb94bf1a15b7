# Farcaster Bot - Registrasi Akun Hemat Biaya 💰

Bot untuk registrasi akun Farcaster secara otomatis dengan **HEMAT BIAYA**:
- 🔥 **Via App Farcaster**: ~$1.00
- ✅ **Via Script Onchain**: ~$0.20
- 💰 **PENGHEMATAN**: ~$0.80 per akun!

## 🚨 PENTING - Baca Sebelum Menggunakan!

Script ini telah diperbaiki sesuai dengan **dokumentasi resmi Farcaster** dan menggunakan:
- ✅ **Optimism Mainnet** (bukan Base)
- ✅ **Bundler contract** untuk registrasi lengkap
- ✅ **App FID** dan **User registration** terpisah
- ✅ **Ed25519 keypair** untuk account key
- ✅ **EIP-712 signatures** yang benar

## 📋 Persyaratan

1. **2 Wallet dengan ETH di Optimism Mainnet**:
   - **App Wallet**: Untuk registrasi App FID
   - **User Wallet**: Untuk user yang akan didaftarkan
   - Minimal 0.01 ETH di masing-masing wallet untuk gas fee

2. **Node.js** versi 16 atau lebih baru

## 🛠️ Instalasi

1. **Clone atau download script ini**
2. **Install dependencies**:
   ```bash
   npm install
   ```

## ⚙️ Cara Menggunakan (AUTO WALLET!)

**TIDAK PERLU KONFIGURASI!** Script otomatis generate wallet baru.

### 🚀 **Super Simple Flow:**

1. **Lihat demo dulu** (tanpa registrasi real):
   ```bash
   npm install
   npm run demo
   ```

2. **Cek harga real-time**:
   ```bash
   npm run price
   ```

3. **Jalankan registrasi real**:
   ```bash
   npm start
   ```

4. **Script akan:**
   - ✅ Generate wallet baru otomatis
   - ✅ Hitung biaya exact yang dibutuhkan
   - ✅ Tampilkan QR code untuk transfer
   - ✅ Monitor balance real-time
   - ✅ Auto eksekusi begitu balance cukup
   - ✅ Simpan semua info ke file JSON

## 📱 **Contoh Output:**

```
🚀 Farcaster Auto Registration - HEMAT BIAYA! 💰

💰 BREAKDOWN BIAYA:
├─ App FID Registration: $0.10
├─ User Registration: $0.20
├─ Gas Fee (estimasi): $0.05
├─ Safety Buffer (20%): $0.07
└─ TOTAL DIBUTUHKAN: 0.000168 ETH (~$0.42)

🎯 Wallet baru dibuat!
📍 Address: 0x1234567890abcdef...

📱 KIRIM ETH KE ADDRESS INI:
┌─────────────────────────────────────────────┐
│  ******************************************  │
└─────────────────────────────────────────────┘

💸 KIRIM EXACTLY: 0.000168 ETH
🔗 Network: Optimism Mainnet

📱 QR Code untuk wallet:
[QR CODE DITAMPILKAN DI SINI]

⏳ Menunggu transfer ETH...
💰 Balance: 0.000168 / 0.000168 ETH [████████████████████] 100.0%
✅ Balance cukup! Memulai registrasi...

🎉 REGISTRASI BERHASIL!
┌─────────────────────────────────────────────┐
│                 SUMMARY                     │
├─────────────────────────────────────────────┤
│ Address: 0x1234567890abcdef...              │
│ Transaction: 0xabcdef1234567890...          │
│ ETH Used: 0.000156 ETH                      │
│ ETH Remaining: 0.000012 ETH                 │
│ Saved vs App: $0.61                        │
└─────────────────────────────────────────────┘
```

## 📊 Proses yang Dilakukan

Script akan melakukan langkah-langkah berikut:

1. **Cek App FID**: Registrasi App FID jika belum ada
2. **Register Signature**: Buat EIP-712 signature untuk registrasi user
3. **Account Key**: Buat Ed25519 keypair untuk user
4. **Signed Key Request**: Buat request yang ditandatangani app
5. **Add Signature**: Buat signature untuk menambah key ke user
6. **Bundler Registration**: Registrasi lengkap melalui Bundler contract

## 📁 Output

Setelah berhasil, script akan membuat:
- **Folder `wallets/`**: Berisi file JSON dengan informasi user
- **File `{user_address}.json`**: Berisi private key, account key, dan transaction hash

## 💰 Perbandingan Biaya

### Via App Farcaster (Mahal):
- **Biaya**: ~$1.00 per akun
- **Kemudahan**: Tinggal klik
- **Total untuk 10 akun**: ~$10.00

### Via Script Onchain (HEMAT):
- **App FID**: ~$0.10 (sekali saja)
- **Per User**: ~$0.20 per akun
- **Total untuk 10 akun**: ~$2.10
- **PENGHEMATAN**: ~$7.90 (79% lebih murah!)

## 🔗 Bridge ETH ke Optimism

Jika Anda belum punya ETH di Optimism:
- **Official Bridge**: https://app.optimism.io/bridge
- **Alternative**: Gunakan exchange yang support Optimism

## ⚠️ Keamanan

- **Jangan share private key** Anda dengan siapa pun
- **Backup file wallet** yang dihasilkan dengan aman
- **Gunakan wallet terpisah** untuk testing

## 🐛 Troubleshooting

### Error: "Wallet tidak punya ETH"
- Pastikan wallet punya ETH di **Optimism Mainnet** (bukan Ethereum mainnet)
- Cek balance di: https://optimistic.etherscan.io/

### Error: "APP_PRIVATE_KEY harus diisi"
- Edit file `bot.js` dan ganti private key di bagian KONFIGURASI

### Error: "Failed to generate signature"
- Pastikan private key format benar (dimulai dengan 0x)
- Pastikan wallet punya ETH untuk gas fee

## 📚 Referensi

- [Dokumentasi Farcaster](https://docs.farcaster.xyz/developers/guides/accounts/create-account)
- [Bundler Contract](https://docs.farcaster.xyz/reference/contracts/reference/bundler)
- [Optimism Bridge](https://app.optimism.io/bridge)
