const { utils } = require('ethers');
const { createPublicClient, http } = require('viem');
const { base } = require('viem/chains');
const {
  ID_GATEWAY_ADDRESS,
  idGatewayABI,
  BUNDLER_ADDRESS,
  bundlerABI,
} = require('@farcaster/hub-nodejs');

// Setup client
const publicClient = createPublicClient({
  chain: base,
  transport: http('https://mainnet.base.org'),
});

async function checkPrices() {
  try {
    console.log('🔍 Mengecek harga registrasi Farcaster...\n');
    
    // Harga App FID (ID Gateway)
    const appFIDPrice = await publicClient.readContract({
      address: ID_GATEWAY_ADDRESS,
      abi: idGatewayABI,
      functionName: 'price',
      args: [0n],
    });
    
    // Harga User Registration (Bundler)
    const userRegPrice = await publicClient.readContract({
      address: BUNDLER_ADDRESS,
      abi: bundlerABI,
      functionName: 'price',
      args: [0n],
    });
    
    // Estimasi harga USD (asumsi ETH = $2500)
    const ethPrice = 2500;
    const appFIDUSD = parseFloat(utils.formatEther(appFIDPrice)) * ethPrice;
    const userRegUSD = parseFloat(utils.formatEther(userRegPrice)) * ethPrice;
    
    console.log('💰 HARGA SAAT INI:');
    console.log('├─ App FID Registration:', utils.formatEther(appFIDPrice), 'ETH (~$' + appFIDUSD.toFixed(2) + ')');
    console.log('└─ User Registration:', utils.formatEther(userRegPrice), 'ETH (~$' + userRegUSD.toFixed(2) + ')');
    
    console.log('\n📊 PERBANDINGAN:');
    console.log('├─ Via App Farcaster: ~$1.00 per akun');
    console.log('├─ Via Script Onchain: ~$' + userRegUSD.toFixed(2) + ' per akun');
    console.log('└─ PENGHEMATAN: ~$' + (1.00 - userRegUSD).toFixed(2) + ' per akun');
    
    const savingsPercent = ((1.00 - userRegUSD) / 1.00 * 100).toFixed(1);
    console.log('\n🎉 Anda menghemat ' + savingsPercent + '% dengan script ini!');
    
    // Estimasi untuk multiple accounts
    console.log('\n📈 ESTIMASI UNTUK MULTIPLE AKUN:');
    [5, 10, 50, 100].forEach(count => {
      const viaApp = count * 1.00;
      const viaScript = appFIDUSD + (count * userRegUSD);
      const savings = viaApp - viaScript;
      console.log(`├─ ${count} akun: Via app $${viaApp.toFixed(2)} vs Script $${viaScript.toFixed(2)} = Hemat $${savings.toFixed(2)}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkPrices();
