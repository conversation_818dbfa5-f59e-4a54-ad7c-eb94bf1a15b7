const fs = require('fs');
const { Wallet, utils } = require('ethers');
const { JsonRpcProvider } = require('@ethersproject/providers');
const { createPublicClient, createWalletClient, http } = require('viem');
const { base } = require('viem/chains');
const {
  ID_GATEWAY_ADDRESS,
  idGatewayABI,
  ID_REGISTRY_ADDRESS,
  idRegistryABI,
  ViemLocalEip712Signer,
} = require('@farcaster/hub-nodejs');

// === Konfigurasi Chain: Base Mainnet ===
const provider = new JsonRpcProvider('https://mainnet.base.org');
const transport = http('https://mainnet.base.org');

// === Wallet & Key Setup ===
async function setupWallet() {
  const wallet = Wallet.createRandom();
  const client = createWalletClient({ chain: base, transport });
  return { wallet, client };
}

// === Ambil <PERSON> (price) dari <PERSON> (gas fee on-chain) ===
async function fetchRegisterPrice(publicClient) {
  const price = await publicClient.readContract({
    address: ID_GATEWAY_ADDRESS,
    abi: idGatewayABI,
    functionName: 'price',
    args: [0n],
  });
  return price;
}

// === Siapkan EIP-712 signature untuk register ===
async function signRegister(walletClient, signer, recovery) {
  const nonce = await signer.signerClient.readContract({
    abi: idRegistryABI,
    address: ID_REGISTRY_ADDRESS,
    functionName: 'nonces',
    args: [signer.address],
  });
  const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600);
  const signerObj = new ViemLocalEip712Signer(signer);
  const sigResult = await signerObj.signRegister({
    to: signer.address,
    recovery,
    nonce,
    deadline,
  });
  if (!sigResult.isOk()) throw new Error('Signing failed');
  return { signature: sigResult.value, deadline };
}

// === Broadcast transaksi on-chain pendaftaran ===
async function broadcastRegister(signer, price, signature, deadline) {
  const tx = await signer.registerFor(
    signer.address,
    "******************************************", // recovery proxy
    { value: price }
  );
  const receipt = await tx.wait();
  return receipt;
}

// === Main Flow ===
(async () => {
  try {
    const { wallet, client: walletClient } = await setupWallet();
    const publicClient = createPublicClient({ chain: base, transport });
    console.log('🎯 Wallet created:', wallet.address);

    // Buat folder wallets jika belum ada
    if (!fs.existsSync('./wallets')) {
      fs.mkdirSync('./wallets');
      console.log('📁 Folder wallets dibuat');
    }

    // Cek balance wallet (harus punya ETH untuk gas fee)
    const balance = await publicClient.getBalance({ address: wallet.address });
    console.log('💰 Balance wallet:', utils.formatEther(balance), 'ETH');

    if (balance === 0n) {
      console.log('❌ Wallet tidak punya ETH! Kirim ETH ke wallet ini dulu:', wallet.address);
      return;
    }

    // Simpan wallet mnemonic & private key
    fs.writeFileSync(`./wallets/${wallet.address}.json`, JSON.stringify({
      address: wallet.address,
      privateKey: wallet.privateKey,
      mnemonic: wallet.mnemonic.phrase
    }, null, 2));
    console.log('💾 Wallet disimpan ke file');

  const price = await fetchRegisterPrice(publicClient);
  console.log('Total price for registration:', price.toString());

  const { signature, deadline } = await signRegister(walletClient, wallet, '******************************************');
  console.log('Register signature obtained');

    const receipt = await broadcastRegister(wallet, price, signature, deadline);
    console.log('✅ Transaction successful. Block:', receipt.blockNumber);
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
})();
