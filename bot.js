const fs = require('fs');
const { utils } = require('ethers');
const ed = require('@noble/ed25519');
const { bytesToHex, createPublicClient, createWalletClient, http } = require('viem');
const { privateKeyToAccount } = require('viem/accounts');
const { optimism } = require('viem/chains');
const {
  ID_GATEWAY_ADDRESS,
  idGatewayABI,
  ID_REGISTRY_ADDRESS,
  idRegistryABI,
  ViemLocalEip712Signer,
  NobleEd25519Signer,
  BUNDLER_ADDRESS,
  bundlerABI,
  KEY_GATEWAY_ADDRESS,
  keyGatewayABI,
} = require('@farcaster/hub-nodejs');

// === Konstanta ===
const FARCASTER_RECOVERY_PROXY = '******************************************';

// === Konfigurasi Chain: Optimism Mainnet (sesuai dokumentasi) ===
const publicClient = createPublicClient({
  chain: optimism,
  transport: http(),
});

const walletClient = createWalletClient({
  chain: optimism,
  transport: http(),
});

// === Setup App Account (perlu private key yang valid) ===
function setupAppAccount(privateKey) {
  if (!privateKey || privateKey === '0x00') {
    throw new Error('❌ APP_PRIVATE_KEY harus diisi dengan private key yang valid!');
  }
  const app = privateKeyToAccount(privateKey);
  const appAccountKey = new ViemLocalEip712Signer(app);
  return { app, appAccountKey };
}

// === Setup User Account ===
function setupUserAccount(privateKey) {
  if (!privateKey || privateKey === '0x00') {
    throw new Error('❌ USER_PRIVATE_KEY harus diisi dengan private key yang valid!');
  }
  const user = privateKeyToAccount(privateKey);
  const userAccountKey = new ViemLocalEip712Signer(user);
  return { user, userAccountKey };
}

// === 1. Register App FID (jika belum punya) ===
async function registerAppFID(app, appAccountKey) {
  console.log('📋 Mengecek App FID...');

  // Cek apakah app sudah punya FID
  const existingFID = await publicClient.readContract({
    address: ID_REGISTRY_ADDRESS,
    abi: idRegistryABI,
    functionName: 'idOf',
    args: [app.address],
  });

  if (existingFID > 0n) {
    console.log('✅ App sudah punya FID:', existingFID.toString());
    return existingFID;
  }

  console.log('🔄 Registrasi App FID baru...');

  // Ambil harga registrasi
  const price = await publicClient.readContract({
    address: ID_GATEWAY_ADDRESS,
    abi: idGatewayABI,
    functionName: 'price',
    args: [0n],
  });

  console.log('💰 Harga registrasi App FID:', utils.formatEther(price), 'ETH');

  // Register app FID
  const { request } = await publicClient.simulateContract({
    account: app,
    address: ID_GATEWAY_ADDRESS,
    abi: idGatewayABI,
    functionName: 'register',
    args: [FARCASTER_RECOVERY_PROXY, 0n],
    value: price,
  });

  await walletClient.writeContract(request);

  // Baca FID yang baru dibuat
  const newFID = await publicClient.readContract({
    address: ID_REGISTRY_ADDRESS,
    abi: idRegistryABI,
    functionName: 'idOf',
    args: [app.address],
  });

  console.log('✅ App FID berhasil dibuat:', newFID.toString());
  return newFID;
}

// === 2. Buat Register Signature untuk User ===
async function createRegisterSignature(userAccountKey, userAddress) {
  console.log('✍️ Membuat Register signature...');

  const nonce = await publicClient.readContract({
    address: ID_REGISTRY_ADDRESS,
    abi: idRegistryABI,
    functionName: 'nonces',
    args: [userAddress],
  });

  const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600);

  const registerSignatureResult = await userAccountKey.signRegister({
    to: userAddress,
    recovery: FARCASTER_RECOVERY_PROXY,
    nonce,
    deadline,
  });

  if (!registerSignatureResult.isOk()) {
    throw new Error('❌ Gagal membuat Register signature');
  }

  console.log('✅ Register signature berhasil dibuat');
  return { signature: registerSignatureResult.value, deadline };
}

// === 3. Buat Account Key untuk User ===
async function createAccountKey() {
  console.log('🔑 Membuat account key...');

  const privateKeyBytes = ed.utils.randomPrivateKey();
  const accountKey = new NobleEd25519Signer(privateKeyBytes);

  const accountKeyResult = await accountKey.getSignerKey();
  if (!accountKeyResult.isOk()) {
    throw new Error('❌ Gagal membuat account key');
  }

  console.log('✅ Account key berhasil dibuat');
  return { accountKey, publicKey: accountKeyResult.value };
}

// === 4. Buat Signed Key Request ===
async function createSignedKeyRequest(appAccountKey, appFID, accountPublicKey) {
  console.log('📝 Membuat Signed Key Request...');

  const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600);

  const signedKeyRequestMetadata = await appAccountKey.getSignedKeyRequestMetadata({
    requestFid: appFID,
    key: accountPublicKey,
    deadline,
  });

  if (!signedKeyRequestMetadata.isOk()) {
    throw new Error('❌ Gagal membuat Signed Key Request');
  }

  console.log('✅ Signed Key Request berhasil dibuat');
  return { metadata: bytesToHex(signedKeyRequestMetadata.value), deadline };
}

// === 5. Buat Add Signature ===
async function createAddSignature(userAccountKey, userAddress, accountPublicKey, metadata, deadline) {
  console.log('✍️ Membuat Add signature...');

  const nonce = await publicClient.readContract({
    address: KEY_GATEWAY_ADDRESS,
    abi: keyGatewayABI,
    functionName: 'nonces',
    args: [userAddress],
  });

  const addSignatureResult = await userAccountKey.signAdd({
    owner: userAddress,
    keyType: 1,
    key: accountPublicKey,
    metadataType: 1,
    metadata,
    nonce,
    deadline,
  });

  if (!addSignatureResult.isOk()) {
    throw new Error('❌ Gagal membuat Add signature');
  }

  console.log('✅ Add signature berhasil dibuat');
  return addSignatureResult.value;
}

// === 6. Register User dengan Bundler ===
async function registerUserWithBundler(app, registerSignature, registerDeadline, accountPublicKey, metadata, addSignature, addDeadline) {
  console.log('� Registrasi user dengan Bundler...');

  // Ambil harga dari Bundler
  const price = await publicClient.readContract({
    address: BUNDLER_ADDRESS,
    abi: bundlerABI,
    functionName: 'price',
    args: [0n],
  });

  console.log('💰 Harga registrasi user:', utils.formatEther(price), 'ETH');

  // Panggil Bundler register
  const { request } = await publicClient.simulateContract({
    account: app,
    address: BUNDLER_ADDRESS,
    abi: bundlerABI,
    functionName: 'register',
    args: [
      {
        to: app.address, // User address
        recovery: FARCASTER_RECOVERY_PROXY,
        sig: bytesToHex(registerSignature),
        deadline: registerDeadline,
      },
      [
        {
          keyType: 1,
          key: bytesToHex(accountPublicKey),
          metadataType: 1,
          metadata: metadata,
          sig: bytesToHex(addSignature),
          deadline: addDeadline,
        },
      ],
      0n,
    ],
    value: price,
  });

  const hash = await walletClient.writeContract(request);
  console.log('✅ Registrasi berhasil! Transaction hash:', hash);

  return hash;
}

// === MAIN FUNCTION ===
async function main() {
  try {
    console.log('🚀 Memulai registrasi akun Farcaster...\n');

    // KONFIGURASI - GANTI DENGAN PRIVATE KEY ANDA!
    const APP_PRIVATE_KEY = '0x00'; // Ganti dengan private key app Anda
    const USER_PRIVATE_KEY = '0x00'; // Ganti dengan private key user yang akan didaftarkan

    if (APP_PRIVATE_KEY === '0x00' || USER_PRIVATE_KEY === '0x00') {
      console.log('❌ PENTING: Anda harus mengisi APP_PRIVATE_KEY dan USER_PRIVATE_KEY!');
      console.log('📝 Edit file bot.js dan ganti nilai private key di bagian KONFIGURASI');
      console.log('💡 Pastikan kedua wallet punya ETH di Optimism Mainnet untuk gas fee');
      return;
    }

    // Buat folder wallets jika belum ada
    if (!fs.existsSync('./wallets')) {
      fs.mkdirSync('./wallets');
      console.log('📁 Folder wallets dibuat\n');
    }

    // Setup accounts
    const { app, appAccountKey } = setupAppAccount(APP_PRIVATE_KEY);
    const { user, userAccountKey } = setupUserAccount(USER_PRIVATE_KEY);

    console.log('👤 App Account:', app.address);
    console.log('👤 User Account:', user.address);

    // Cek balance kedua account
    const appBalance = await publicClient.getBalance({ address: app.address });
    const userBalance = await publicClient.getBalance({ address: user.address });

    console.log('💰 App Balance:', utils.formatEther(appBalance), 'ETH');
    console.log('💰 User Balance:', utils.formatEther(userBalance), 'ETH\n');

    if (appBalance === 0n || userBalance === 0n) {
      console.log('❌ Kedua wallet harus punya ETH di Optimism Mainnet!');
      console.log('🔗 Bridge ETH ke Optimism: https://app.optimism.io/bridge');
      return;
    }

    // Step 1: Register atau ambil App FID
    const appFID = await registerAppFID(app, appAccountKey);

    // Step 2: Buat Register signature untuk user
    const { signature: registerSignature, deadline: registerDeadline } =
      await createRegisterSignature(userAccountKey, user.address);

    // Step 3: Buat account key untuk user
    const { accountKey, publicKey: accountPublicKey } = await createAccountKey();

    // Step 4: Buat Signed Key Request
    const { metadata, deadline: keyDeadline } =
      await createSignedKeyRequest(appAccountKey, appFID, accountPublicKey);

    // Step 5: Buat Add signature
    const addSignature = await createAddSignature(
      userAccountKey,
      user.address,
      accountPublicKey,
      metadata,
      keyDeadline
    );

    // Step 6: Register user dengan Bundler
    const txHash = await registerUserWithBundler(
      app,
      registerSignature,
      registerDeadline,
      accountPublicKey,
      metadata,
      addSignature,
      keyDeadline
    );

    // Simpan informasi user
    const userInfo = {
      address: user.address,
      privateKey: USER_PRIVATE_KEY,
      accountKey: {
        privateKey: Buffer.from(accountKey.privateKey).toString('hex'),
        publicKey: Buffer.from(accountPublicKey).toString('hex'),
      },
      transactionHash: txHash,
      timestamp: new Date().toISOString(),
    };

    fs.writeFileSync(`./wallets/${user.address}.json`, JSON.stringify(userInfo, null, 2));
    console.log('💾 Informasi user disimpan ke file\n');

    console.log('🎉 REGISTRASI BERHASIL!');
    console.log('📋 User Address:', user.address);
    console.log('🔗 Transaction:', txHash);
    console.log('📁 Detail tersimpan di: ./wallets/' + user.address + '.json');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('📋 Detail error:', error);
  }
}

// Jalankan main function
main();
