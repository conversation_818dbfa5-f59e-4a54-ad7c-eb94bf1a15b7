const fs = require('fs');
const { utils, Wallet } = require('ethers');
const ed = require('@noble/ed25519');
const { bytesToHex, createPublicClient, createWalletClient, http } = require('viem');
const { privateKeyToAccount } = require('viem/accounts');
const { optimism } = require('viem/chains');
const qrcode = require('qrcode-terminal');
const {
  ID_GATEWAY_ADDRESS,
  idGatewayABI,
  ID_REGISTRY_ADDRESS,
  idRegistryABI,
  ViemLocalEip712Signer,
  NobleEd25519Signer,
  BUNDLER_ADDRESS,
  bundlerABI,
  KEY_GATEWAY_ADDRESS,
  keyGatewayABI,
} = require('@farcaster/hub-nodejs');

// === Konstanta ===
const FARCASTER_RECOVERY_PROXY = '******************************************';

// === Konfigurasi Chain: Optimism Mainnet (tempat Farcaster contracts) ===
// Menggunakan RPC gratis untuk menghemat biaya
const publicClient = createPublicClient({
  chain: optimism,
  transport: http('https://mainnet.optimism.io'), // RPC gratis Optimism
});

const walletClient = createWalletClient({
  chain: optimism,
  transport: http('https://mainnet.optimism.io'),
});

// === Setup App Account (perlu private key yang valid) ===
function setupAppAccount(privateKey) {
  if (!privateKey || privateKey === '0x00') {
    throw new Error('❌ APP_PRIVATE_KEY harus diisi dengan private key yang valid!');
  }
  const app = privateKeyToAccount(privateKey);
  const appAccountKey = new ViemLocalEip712Signer(app);
  return { app, appAccountKey };
}

// === Setup User Account ===
function setupUserAccount(privateKey) {
  if (!privateKey || privateKey === '0x00') {
    throw new Error('❌ USER_PRIVATE_KEY harus diisi dengan private key yang valid!');
  }
  const user = privateKeyToAccount(privateKey);
  const userAccountKey = new ViemLocalEip712Signer(user);
  return { user, userAccountKey };
}

// === Auto Generate Wallet ===
function generateNewWallet() {
  const wallet = Wallet.createRandom();
  console.log('🎯 Wallet baru dibuat!');
  console.log('📍 Address:', wallet.address);
  console.log('🔑 Private Key:', wallet.privateKey);
  console.log('📝 Mnemonic:', wallet.mnemonic.phrase);

  return {
    address: wallet.address,
    privateKey: wallet.privateKey,
    mnemonic: wallet.mnemonic.phrase
  };
}

// === Kalkulasi Total Biaya ===
async function calculateTotalCost() {
  console.log('💰 Menghitung biaya yang dibutuhkan...');

  try {
    // Ambil harga App FID
    const appFIDPrice = await publicClient.readContract({
      address: ID_GATEWAY_ADDRESS,
      abi: idGatewayABI,
      functionName: 'price',
      args: [0n],
    });

    // Ambil harga User Registration
    const userRegPrice = await publicClient.readContract({
      address: BUNDLER_ADDRESS,
      abi: bundlerABI,
      functionName: 'price',
      args: [0n],
    });

    // Estimasi gas fee (konservatif)
    const estimatedGas = utils.parseEther('0.002'); // ~$5 worth of gas

    // Buffer 20% untuk safety
    const totalBeforeBuffer = appFIDPrice + userRegPrice + estimatedGas;
    const buffer = totalBeforeBuffer / 5n; // 20%
    const totalWithBuffer = totalBeforeBuffer + buffer;

    return {
      appFIDPrice,
      userRegPrice,
      estimatedGas,
      buffer,
      total: totalWithBuffer,
      breakdown: {
        appFID: parseFloat(utils.formatEther(appFIDPrice)),
        userReg: parseFloat(utils.formatEther(userRegPrice)),
        gas: parseFloat(utils.formatEther(estimatedGas)),
        buffer: parseFloat(utils.formatEther(buffer)),
        total: parseFloat(utils.formatEther(totalWithBuffer))
      }
    };
  } catch (error) {
    console.error('❌ Error menghitung biaya:', error.message);
    throw error;
  }
}

// === Monitor Balance ===
async function waitForBalance(address, requiredAmount) {
  console.log('⏳ Menunggu transfer ETH...\n');

  const requiredETH = parseFloat(utils.formatEther(requiredAmount));
  let attempts = 0;
  const maxAttempts = 1200; // 10 menit (5 detik interval)

  while (attempts < maxAttempts) {
    try {
      const balance = await publicClient.getBalance({ address });
      const balanceETH = parseFloat(utils.formatEther(balance));

      // Progress bar
      const percentage = Math.min((balanceETH / requiredETH) * 100, 100);
      const progressBar = '█'.repeat(Math.floor(percentage / 5)) + '░'.repeat(20 - Math.floor(percentage / 5));

      process.stdout.write(`\r💰 Balance: ${balanceETH.toFixed(6)} / ${requiredETH.toFixed(6)} ETH [${progressBar}] ${percentage.toFixed(1)}%`);

      if (balance >= requiredAmount) {
        console.log('\n✅ Balance cukup! Memulai registrasi...\n');
        return balance;
      }

      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
      attempts++;
    } catch (error) {
      console.error('\n❌ Error checking balance:', error.message);
      await new Promise(resolve => setTimeout(resolve, 5000));
      attempts++;
    }
  }

  throw new Error('❌ Timeout: Balance tidak cukup setelah 10 menit');
}

// === 1. Register App FID (jika belum punya) ===
async function registerAppFID(app, appAccountKey) {
  console.log('📋 Mengecek App FID...');

  // Cek apakah app sudah punya FID
  const existingFID = await publicClient.readContract({
    address: ID_REGISTRY_ADDRESS,
    abi: idRegistryABI,
    functionName: 'idOf',
    args: [app.address],
  });

  if (existingFID > 0n) {
    console.log('✅ App sudah punya FID:', existingFID.toString());
    return existingFID;
  }

  console.log('🔄 Registrasi App FID baru...');

  // Ambil harga registrasi
  const price = await publicClient.readContract({
    address: ID_GATEWAY_ADDRESS,
    abi: idGatewayABI,
    functionName: 'price',
    args: [0n],
  });

  const priceUSD = parseFloat(utils.formatEther(price)) * 2500; // Estimasi ETH = $2500
  console.log('💰 Harga registrasi App FID:', utils.formatEther(price), 'ETH (~$' + priceUSD.toFixed(2) + ')');
  console.log('💡 Bandingkan: Via app Farcaster ~$1, via onchain ~$0.2!');

  // Register app FID
  const { request } = await publicClient.simulateContract({
    account: app,
    address: ID_GATEWAY_ADDRESS,
    abi: idGatewayABI,
    functionName: 'register',
    args: [FARCASTER_RECOVERY_PROXY, 0n],
    value: price,
  });

  await walletClient.writeContract(request);

  // Baca FID yang baru dibuat
  const newFID = await publicClient.readContract({
    address: ID_REGISTRY_ADDRESS,
    abi: idRegistryABI,
    functionName: 'idOf',
    args: [app.address],
  });

  console.log('✅ App FID berhasil dibuat:', newFID.toString());
  return newFID;
}

// === 2. Buat Register Signature untuk User ===
async function createRegisterSignature(userAccountKey, userAddress) {
  console.log('✍️ Membuat Register signature...');

  const nonce = await publicClient.readContract({
    address: ID_REGISTRY_ADDRESS,
    abi: idRegistryABI,
    functionName: 'nonces',
    args: [userAddress],
  });

  const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600);

  const registerSignatureResult = await userAccountKey.signRegister({
    to: userAddress,
    recovery: FARCASTER_RECOVERY_PROXY,
    nonce,
    deadline,
  });

  if (!registerSignatureResult.isOk()) {
    throw new Error('❌ Gagal membuat Register signature');
  }

  console.log('✅ Register signature berhasil dibuat');
  return { signature: registerSignatureResult.value, deadline };
}

// === 3. Buat Account Key untuk User ===
async function createAccountKey() {
  console.log('🔑 Membuat account key...');

  const privateKeyBytes = ed.utils.randomPrivateKey();
  const accountKey = new NobleEd25519Signer(privateKeyBytes);

  const accountKeyResult = await accountKey.getSignerKey();
  if (!accountKeyResult.isOk()) {
    throw new Error('❌ Gagal membuat account key');
  }

  console.log('✅ Account key berhasil dibuat');
  return { accountKey, publicKey: accountKeyResult.value };
}

// === 4. Buat Signed Key Request ===
async function createSignedKeyRequest(appAccountKey, appFID, accountPublicKey) {
  console.log('📝 Membuat Signed Key Request...');

  const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600);

  const signedKeyRequestMetadata = await appAccountKey.getSignedKeyRequestMetadata({
    requestFid: appFID,
    key: accountPublicKey,
    deadline,
  });

  if (!signedKeyRequestMetadata.isOk()) {
    throw new Error('❌ Gagal membuat Signed Key Request');
  }

  console.log('✅ Signed Key Request berhasil dibuat');
  return { metadata: bytesToHex(signedKeyRequestMetadata.value), deadline };
}

// === 5. Buat Add Signature ===
async function createAddSignature(userAccountKey, userAddress, accountPublicKey, metadata, deadline) {
  console.log('✍️ Membuat Add signature...');

  const nonce = await publicClient.readContract({
    address: KEY_GATEWAY_ADDRESS,
    abi: keyGatewayABI,
    functionName: 'nonces',
    args: [userAddress],
  });

  const addSignatureResult = await userAccountKey.signAdd({
    owner: userAddress,
    keyType: 1,
    key: accountPublicKey,
    metadataType: 1,
    metadata,
    nonce,
    deadline,
  });

  if (!addSignatureResult.isOk()) {
    throw new Error('❌ Gagal membuat Add signature');
  }

  console.log('✅ Add signature berhasil dibuat');
  return addSignatureResult.value;
}

// === 6. Register User dengan Bundler ===
async function registerUserWithBundler(app, registerSignature, registerDeadline, accountPublicKey, metadata, addSignature, addDeadline) {
  console.log('� Registrasi user dengan Bundler...');

  // Ambil harga dari Bundler
  const price = await publicClient.readContract({
    address: BUNDLER_ADDRESS,
    abi: bundlerABI,
    functionName: 'price',
    args: [0n],
  });

  const priceUSD = parseFloat(utils.formatEther(price)) * 2500; // Estimasi ETH = $2500
  console.log('💰 Harga registrasi user:', utils.formatEther(price), 'ETH (~$' + priceUSD.toFixed(2) + ')');
  console.log('🎉 HEMAT: Anda menghemat ~$0.8 dibanding via app!');

  // Panggil Bundler register
  const { request } = await publicClient.simulateContract({
    account: app,
    address: BUNDLER_ADDRESS,
    abi: bundlerABI,
    functionName: 'register',
    args: [
      {
        to: app.address, // User address
        recovery: FARCASTER_RECOVERY_PROXY,
        sig: bytesToHex(registerSignature),
        deadline: registerDeadline,
      },
      [
        {
          keyType: 1,
          key: bytesToHex(accountPublicKey),
          metadataType: 1,
          metadata: metadata,
          sig: bytesToHex(addSignature),
          deadline: addDeadline,
        },
      ],
      0n,
    ],
    value: price,
  });

  const hash = await walletClient.writeContract(request);
  console.log('✅ Registrasi berhasil! Transaction hash:', hash);

  return hash;
}

// === MAIN FUNCTION ===
async function main() {
  try {
    console.log('🚀 Farcaster Auto Registration - HEMAT BIAYA! 💰\n');
    console.log('💡 Via app: ~$1.00 | Via script: ~$0.20 | HEMAT: ~80%!\n');

    // Step 1: Kalkulasi biaya total
    const costInfo = await calculateTotalCost();

    console.log('💰 BREAKDOWN BIAYA:');
    console.log('├─ App FID Registration: $' + (costInfo.breakdown.appFID * 2500).toFixed(2));
    console.log('├─ User Registration: $' + (costInfo.breakdown.userReg * 2500).toFixed(2));
    console.log('├─ Gas Fee (estimasi): $' + (costInfo.breakdown.gas * 2500).toFixed(2));
    console.log('├─ Safety Buffer (20%): $' + (costInfo.breakdown.buffer * 2500).toFixed(2));
    console.log('└─ TOTAL DIBUTUHKAN: ' + costInfo.breakdown.total.toFixed(6) + ' ETH (~$' + (costInfo.breakdown.total * 2500).toFixed(2) + ')\n');

    // Step 2: Generate wallet baru
    const walletInfo = generateNewWallet();

    // Step 3: Tampilkan QR code dan instruksi
    console.log('📱 KIRIM ETH KE ADDRESS INI:');
    console.log('┌─────────────────────────────────────────────┐');
    console.log('│  ' + walletInfo.address + '  │');
    console.log('└─────────────────────────────────────────────┘\n');

    console.log('� KIRIM EXACTLY: ' + costInfo.breakdown.total.toFixed(6) + ' ETH');
    console.log('🔗 Network: Optimism Mainnet');
    console.log('⚠️  PENTING: Kirim ke Optimism, bukan Ethereum mainnet!\n');

    // QR Code
    console.log('📱 QR Code untuk wallet:');
    qrcode.generate(walletInfo.address, { small: true });
    console.log('');

    // Step 4: Wait for balance
    const actualBalance = await waitForBalance(walletInfo.address, costInfo.total);

    // Step 5: Setup accounts dengan wallet yang sudah ada balance
    const { user, userAccountKey } = setupUserAccount(walletInfo.privateKey);

    // Untuk App FID, kita gunakan wallet yang sama (bisa dipisah jika mau)
    const { app, appAccountKey } = setupAppAccount(walletInfo.privateKey);

    console.log('✅ Wallet siap dengan balance:', utils.formatEther(actualBalance), 'ETH');
    console.log('� Address:', user.address, '\n');

    // Buat folder wallets jika belum ada
    if (!fs.existsSync('./wallets')) {
      fs.mkdirSync('./wallets');
      console.log('� Folder wallets dibuat');
    }

    // Step 1: Register atau ambil App FID
    const appFID = await registerAppFID(app, appAccountKey);

    // Step 2: Buat Register signature untuk user
    const { signature: registerSignature, deadline: registerDeadline } =
      await createRegisterSignature(userAccountKey, user.address);

    // Step 3: Buat account key untuk user
    const { accountKey, publicKey: accountPublicKey } = await createAccountKey();

    // Step 4: Buat Signed Key Request
    const { metadata, deadline: keyDeadline } =
      await createSignedKeyRequest(appAccountKey, appFID, accountPublicKey);

    // Step 5: Buat Add signature
    const addSignature = await createAddSignature(
      userAccountKey,
      user.address,
      accountPublicKey,
      metadata,
      keyDeadline
    );

    // Step 6: Register user dengan Bundler
    const txHash = await registerUserWithBundler(
      app,
      registerSignature,
      registerDeadline,
      accountPublicKey,
      metadata,
      addSignature,
      keyDeadline
    );

    // Step 7: Simpan informasi lengkap
    const finalBalance = await publicClient.getBalance({ address: user.address });
    const usedETH = actualBalance - finalBalance;
    const savedUSD = (1.00 - parseFloat(utils.formatEther(usedETH)) * 2500);

    const userInfo = {
      address: user.address,
      privateKey: walletInfo.privateKey,
      mnemonic: walletInfo.mnemonic,
      accountKey: {
        privateKey: Buffer.from(accountKey.privateKey).toString('hex'),
        publicKey: Buffer.from(accountPublicKey).toString('hex'),
      },
      transactionHash: txHash,
      costs: {
        totalSent: utils.formatEther(actualBalance),
        totalUsed: utils.formatEther(usedETH),
        remaining: utils.formatEther(finalBalance),
        savedVsApp: '$' + savedUSD.toFixed(2)
      },
      timestamp: new Date().toISOString(),
    };

    fs.writeFileSync(`./wallets/${user.address}.json`, JSON.stringify(userInfo, null, 2));

    console.log('🎉 REGISTRASI BERHASIL!');
    console.log('┌─────────────────────────────────────────────┐');
    console.log('│                 SUMMARY                     │');
    console.log('├─────────────────────────────────────────────┤');
    console.log('│ Address: ' + user.address.substring(0, 20) + '...     │');
    console.log('│ Transaction: ' + txHash.substring(0, 20) + '...        │');
    console.log('│ ETH Used: ' + utils.formatEther(usedETH).substring(0, 8) + ' ETH                  │');
    console.log('│ ETH Remaining: ' + utils.formatEther(finalBalance).substring(0, 8) + ' ETH           │');
    console.log('│ Saved vs App: $' + savedUSD.toFixed(2) + '                    │');
    console.log('└─────────────────────────────────────────────┘');
    console.log('� Detail lengkap tersimpan di: ./wallets/' + user.address + '.json');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('📋 Detail error:', error);
  }
}

// Jalankan main function
main();
